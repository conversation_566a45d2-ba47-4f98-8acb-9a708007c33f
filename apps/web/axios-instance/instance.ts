import axios, { AxiosError } from "axios";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3005";

const instance = axios.create({
  baseURL: `${BASE_URL}/api/v1`,
  headers: {
    "Content-Type": "application/json",
  },
});

const authinstance = axios.create({
  baseURL: `${BASE_URL}/auth`,
  headers: {
    "Content-Type": "application/json",
  },
});

// ✅ Custom APIError class — looks & behaves like native Error
class APIError extends Error {
  status?: number;
  code?: string;
  meta?: any;
  constructor(message: string, status?: number, code?: string, meta?: any) {
    super(message);
    this.name = "APIError";
    this.status = status;
    this.code = code;
    this.meta = meta;
  }
}

// ✅ Safe localStorage read for Next.js
const getTenantId = () => {
  if (typeof window !== "undefined") {
    return localStorage.getItem("currentTenantId");
  }
  return null;
};

// ✅ Request interceptor
const attachTenantId = (config: any) => {
  const tenantId = getTenantId();
  if (tenantId) {
    config.headers["x-tenant-id"] = tenantId;
  }
  return config;
};

// ✅ Unified response error handler
const handleResponseError = (error: AxiosError) => {
  console.log(error);
  if (error.response) {
    const { status, data } = error.response as any;

    // Expected backend format:
    // { success: false, error: { code, message }, meta?: {...} }
    const backendError = data?.error;
    const message = backendError?.message || data?.message || "An unexpected error occurred";
    const code = backendError?.code;
    const meta = data?.meta;

    if (process.env.NODE_ENV !== "production") {
      console.error("API Error:", { status, code, message, meta });
    }

    // ✅ Throw a native-like Error with extra fields
    return Promise.reject(new APIError(message, status, code, meta));
  }

  if (error.request) {
    return Promise.reject(
      new APIError(
        "No response from server. Please check your connection.",
        undefined,
        "NO_RESPONSE"
      )
    );
  }

  return Promise.reject(new APIError(error.message || "Unexpected error", undefined, "UNKNOWN"));
};

// ✅ Attach interceptors
instance.interceptors.request.use(attachTenantId, Promise.reject);
instance.interceptors.response.use((r) => r, handleResponseError);

authinstance.interceptors.request.use(attachTenantId, Promise.reject);
authinstance.interceptors.response.use((r) => r, handleResponseError);

export { authinstance, APIError };
export { instance };
