"use client";

import { useEffect, useState } from "react";
import { Di<PERSON>, DialogContent } from "@repo/ui/components/dialog";
import { PdfViewer, type PageMark } from "@/components/pdf-viewer/pdf-viewer";
import { Skeleton } from "@repo/ui/components/skeleton";
import { Icons } from "@repo/ui/components/icons";
import { instance } from "@/axios-instance";

interface PdfViewerDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectId: string;
  fileId: string;
  fileName?: string;
}

export function PdfViewerDialog({
  open,
  onOpenChange,
  projectId,
  fileId,
  fileName,
}: PdfViewerDialogProps) {
  const [marks, setMarks] = useState<PageMark[]>([]);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [pdfError, setPdfError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Load PDF file
  useEffect(() => {
    if (!open || !projectId || !fileId) return;

    const fetchPdf = async () => {
      setIsLoading(true);
      setPdfError(null);

      try {
        const response = await instance.get(`/projects/${projectId}/files/${fileId}`, {
          responseType: "blob",
        });
        const blob = new Blob([response.data], { type: "application/pdf" });
        const url = URL.createObjectURL(blob);
        setPdfUrl(url);
      } catch (error) {
        console.error("Error loading PDF:", error);
        setPdfError("Failed to load PDF file");
      } finally {
        setIsLoading(false);
      }
    };

    fetchPdf();

    return () => {
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
        setPdfUrl(null);
      }
    };
  }, [open, projectId, fileId]);

  // Cleanup on dialog close
  useEffect(() => {
    if (!open && pdfUrl) {
      URL.revokeObjectURL(pdfUrl);
      setPdfUrl(null);
      setMarks([]);
      setPdfError(null);
    }
  }, [open, pdfUrl]);

  const handleClose = () => onOpenChange(false);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        showCloseButton={false}
        className="min-w-screen max-w-screen h-screen max-h-screen overflow-auto p-0"
      >
        <div className="flex h-full w-full flex-col">
          {/* Header */}
          <div className="flex shrink-0 items-center justify-between border-b bg-white px-6 py-4">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">{fileName || "PDF Viewer"}</h2>
              <p className="text-sm text-gray-500">Select job and spare parts for this document</p>
            </div>
            <button
              onClick={handleClose}
              className="rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-600"
            >
              <Icons.x className="h-5 w-5" />
            </button>
          </div>

          {/* PDF Area */}
          <div className="flex-1 overflow-hidden bg-gray-50">
            {isLoading && (
              <div className="flex h-full w-full items-center justify-center">
                <div className="space-y-4">
                  <Skeleton className="h-8 w-48" />
                  <Skeleton className="h-96 w-80" />
                </div>
              </div>
            )}

            {pdfError && (
              <div className="flex h-full w-full items-center justify-center">
                <div className="flex max-w-md items-center gap-3 rounded-lg border border-red-200 bg-red-50 p-4 text-red-800">
                  <Icons.alertCircle className="h-5 w-5 flex-shrink-0" />
                  <p className="text-sm">{pdfError}</p>
                </div>
              </div>
            )}

            {pdfUrl && !isLoading && !pdfError && (
              <PdfViewer pdfUrl={pdfUrl} value={marks} onChange={setMarks} className="h-full" />
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
