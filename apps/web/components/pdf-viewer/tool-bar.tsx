"use client";

import { Minus, Plus, RotateCw } from "lucide-react";
import { cn } from "@repo/ui/lib/utils";
import type { ToolbarProps } from "./types";
import { ChevronLeft, ChevronRight, Trash2, Filter } from "lucide-react";
import { Button } from "@repo/ui/components/button";

export function Toolbar({
  pageNum,
  scale,
  onZoomIn,
  onZoomOut,
  onRotate,
  checked,
  onChangeChecked,
  onClearBBoxes,
  disabled,
}: {
  pageNum?: number;
  scale: number;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onRotate: () => void;
  checked: { job: boolean; spare: boolean };
  onChangeChecked: (key: "job" | "spare", val: boolean) => void;
  onClearBBoxes?: () => void;
  disabled?: boolean;
}) {
  return (
    <div className="flex items-center justify-between gap-2">
      <div className="flex items-center gap-2">
        <Button onClick={onZoomOut} variant="outline" size="icon">
          -
        </Button>
        <Button onClick={onZoomIn} variant="outline" size="icon">
          +
        </Button>
      </div>

      <div className="flex items-center gap-2">
        <label className="flex items-center gap-1 text-sm">
          <input
            type="checkbox"
            checked={checked.job}
            onChange={(e) => onChangeChecked("job", e.target.checked)}
          />
          Job
        </label>
        <label className="flex items-center gap-1 text-sm">
          <input
            type="checkbox"
            checked={checked.spare}
            onChange={(e) => onChangeChecked("spare", e.target.checked)}
          />
          Spare
        </label>
      </div>
      <Button onClick={onRotate} variant="outline" size="icon">
        ⟳
      </Button>

      {/* <Button
        onClick={onClearBBoxes}
        variant="destructive"
        size="sm"
        disabled={disabled}
        className="flex items-center gap-1"
      >
        <Trash2 className="h-4 w-4" /> Clear
      </Button> */}
    </div>
  );
}
