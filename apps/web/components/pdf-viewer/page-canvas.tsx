export function PageCanvas({
  pageNum,
  canvasRef,
  overlayRef,
  scale,
}: {
  pageNum: number;
  canvasRef: React.RefObject<HTMLCanvasElement | null>;
  overlayRef: React.RefObject<HTMLDivElement | null>;
  scale: number;
}) {
  return (
    <div className="bg-background ring-border relative mt-3 flex min-h-0 items-center justify-center rounded-lg p-3 ring-1">
      <div
        className="relative origin-top-left"
        style={{
          transform: `scale(${scale})`,
          transformOrigin: "top left",
        }}
      >
        <canvas ref={canvasRef} className="block" />
        <div ref={overlayRef} className="absolute inset-0" />
      </div>
    </div>
  );
}
