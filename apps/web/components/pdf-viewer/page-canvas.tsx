import { BBox } from "./pdf-viewer";

export function PageCanvas({
  pageNum,
  canvasRef,
  overlayRef,
  bboxes,
  drawing,
  handleMouseDown,
  handleMouseMove,
  handleMouseUp,
}: {
  pageNum: number;
  canvasRef: React.RefObject<HTMLCanvasElement | null>;
  overlayRef: React.RefObject<HTMLDivElement | null>;
  bboxes: BBox[];
  drawing: any;
  handleMouseDown: (e: React.MouseEvent, page: number) => void;
  handleMouseMove: (e: React.MouseEvent) => void;
  handleMouseUp: () => void;
}) {
  return (
    <div className="bg-background ring-border relative mt-3 flex items-center justify-center overflow-hidden rounded-lg p-3 ring-1">
      <div
        className="relative"
        style={{
          transform: `scale(1)`,
          transformOrigin: "top left",
        }}
      >
        <canvas ref={canvasRef} />
        <div
          ref={overlayRef}
          className="absolute inset-0 cursor-crosshair"
          onMouseDown={(e) => handleMouseDown(e, pageNum)}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
        >
          {bboxes.map((b, i) => (
            <div
              key={i}
              className="absolute border-2 border-blue-500 bg-blue-500/10"
              style={{
                left: b.x,
                top: b.y,
                width: b.width,
                height: b.height,
              }}
            />
          ))}
          {drawing?.page === pageNum && (
            <div
              className="absolute border-2 border-green-500 bg-green-500/10"
              style={{
                left: drawing.x,
                top: drawing.y,
                width: drawing.width,
                height: drawing.height,
              }}
            />
          )}
        </div>
      </div>
    </div>
  );
}
