import type { PDFDocumentProxy, RenderTask } from "pdfjs-dist";

export type JobSpareMap = Record<number, { job: boolean; spare: boolean }>;

export interface PdfViewerProps {
    pdfUrl: string;
    open: boolean;
    initialPage?: number;
    className?: string;
    value?: PageMark[]; // incoming marks
    onChange?: (updated: PageMark[]) => void; // notify parent
}

export type ToolbarProps = {
    pageNum?: number;
    scale: number;
    onZoomIn: () => void;
    onZoomOut: () => void;
    onRotate: () => void;
    checked: { job: boolean; spare: boolean };
    onChangeChecked: (key: "job" | "spare", value: boolean) => void;
    onClearBBoxes?: () => void;
    disabled?: boolean;
};

export type PageMark = { page: number; job: boolean; spare: boolean };