"use client";

import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from "react";
import { ChevronLeft, ChevronRight, Filter } from "lucide-react";
import { cn } from "@repo/ui/lib/utils";
import { getDocument, type PDFDocumentProxy, type RenderTask } from "pdfjs-dist";
import { usePdfWorker } from "./usePdfWorker";
import { clamp } from "./utils";
import { Button } from "@repo/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { Skeleton } from "@repo/ui/components/skeleton";
import { PageCanvas } from "./page-canvas";
import { Toolbar } from "./tool-bar";

/* ----------------------------- Types ----------------------------- */

export interface PageMark {
  page: number;
  job: boolean;
  spare: boolean;
  rotation?: 0 | 90 | 180 | 270;
}

export interface PdfViewerProps {
  pdfUrl: string;
  initialPage?: number;
  className?: string;
  value: PageMark[];
  onChange: (marks: PageMark[]) => void;
}

/* ----------------------------- Component ----------------------------- */

export function PdfViewer({ pdfUrl, initialPage = 1, className, value, onChange }: PdfViewerProps) {
  usePdfWorker();

  console.log("pdf value", value);

  const [doc, setDoc] = useState<PDFDocumentProxy | null>(null);
  const [numPages, setNumPages] = useState(0);
  const [leftPage, setLeftPage] = useState(() =>
    Math.max(1, initialPage - (initialPage % 2 === 0 ? 1 : 0))
  );
  const [scale, setScale] = useState(1);
  const [rotationMap, setRotationMap] = useState<Record<number, 0 | 90 | 180 | 270>>({});
  const [loadingError, setLoadingError] = useState<string | null>(null);
  const [filter, setFilter] = useState<"job" | "spare" | "both">("both");
  const [isLoading, setIsLoading] = useState(true);

  const leftCanvasRef = useRef<HTMLCanvasElement | null>(null);
  const rightCanvasRef = useRef<HTMLCanvasElement | null>(null);
  const leftOverlayRef = useRef<HTMLDivElement | null>(null);
  const rightOverlayRef = useRef<HTMLDivElement | null>(null);
  const leftTaskRef = useRef<RenderTask | null>(null);
  const rightTaskRef = useRef<RenderTask | null>(null);

  /* ----------------------------- Navigation ----------------------------- */
  const nextSpread = () => setLeftPage((p) => (p + 2 <= numPages ? p + 2 : p));
  const prevSpread = () => setLeftPage((p) => (p - 2 >= 1 ? p - 2 : p));

  const zoomIn = () => setScale((s) => clamp(Number((s + 0.1).toFixed(2)), 0.5, 3));
  const zoomOut = () => setScale((s) => clamp(Number((s - 0.1).toFixed(2)), 0.5, 3));

  /* ----------------------------- Prevent Browser Zoom ----------------------------- */
  useEffect(() => {
    const handleWheel = (e: WheelEvent) => {
      if (e.ctrlKey) {
        e.preventDefault();
        if (e.deltaY < 0) zoomIn();
        else zoomOut();
      }
    };

    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && (e.key === "+" || e.key === "=")) {
        e.preventDefault();
        zoomIn();
      } else if ((e.ctrlKey || e.metaKey) && e.key === "-") {
        e.preventDefault();
        zoomOut();
      }
    };

    window.addEventListener("wheel", handleWheel, { passive: false });
    window.addEventListener("keydown", handleKeyDown);

    return () => {
      window.removeEventListener("wheel", handleWheel);
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, []);

  /* ----------------------------- Rotation ----------------------------- */
  const rotatePage = (pageNum: number) => {
    setRotationMap((prev) => {
      const current = prev[pageNum] ?? 0;
      const newRotation = ((current + 90) % 360) as 0 | 90 | 180 | 270;

      const updatedMarks = value.map((m) =>
        m.page === pageNum ? { ...m, rotation: newRotation } : m
      );
      const exists = value.find((m) => m.page === pageNum);
      if (!exists)
        updatedMarks.push({
          page: pageNum,
          job: false,
          spare: false,
          rotation: newRotation,
        });
      onChange(updatedMarks);

      return { ...prev, [pageNum]: newRotation };
    });
  };

  /* ----------------------------- Page Marks ----------------------------- */
  const getMarkForPage = useCallback(
    (page: number) =>
      value.find((v) => v.page === page) || { page, job: false, spare: false, rotation: 0 },
    [value]
  );

  const setCheck = (pageNum: number, key: "job" | "spare", val: boolean) => {
    const updated = value.map((m) => (m.page === pageNum ? { ...m, [key]: val } : m));
    const exists = value.find((m) => m.page === pageNum);
    if (!exists)
      updated.push({
        page: pageNum,
        job: key === "job" ? val : false,
        spare: key === "spare" ? val : false,
        rotation: rotationMap[pageNum] ?? 0,
      });
    onChange(updated);
  };

  /* ----------------------------- PDF Load ----------------------------- */
  useEffect(() => {
    let cancelled = false;
    setLoadingError(null);
    setIsLoading(true);

    getDocument({ url: pdfUrl })
      .promise.then((pdf) => {
        if (cancelled) return;
        setDoc(pdf);
        setNumPages(pdf.numPages);
        setLeftPage((prev) => (prev % 2 === 0 ? prev - 1 : prev));
        setIsLoading(false);
      })
      .catch(() => {
        setLoadingError("Failed to load PDF.");
        setIsLoading(false);
      });

    return () => {
      cancelled = true;
    };
  }, [pdfUrl]);

  const rightPage = useMemo(
    () => (leftPage + 1 <= numPages ? leftPage + 1 : null),
    [leftPage, numPages]
  );

  /* ----------------------------- Rendering ----------------------------- */
  const cancelRenderTasks = useCallback(() => {
    leftTaskRef.current?.cancel();
    rightTaskRef.current?.cancel();
    leftTaskRef.current = null;
    rightTaskRef.current = null;
  }, []);

  const renderPageToCanvas = useCallback(
    async (pageNum: number, canvas: HTMLCanvasElement | null): Promise<RenderTask | null> => {
      if (!doc || !canvas) return null;
      try {
        const page = await doc.getPage(pageNum);
        const dpr = window.devicePixelRatio || 1;
        const rotation = rotationMap[pageNum] ?? 0;
        const viewport = page.getViewport({ scale, rotation });
        const ctx = canvas.getContext("2d", { alpha: false });
        if (!ctx) return null;

        canvas.width = viewport.width * dpr;
        canvas.height = viewport.height * dpr;
        canvas.style.width = `${viewport.width}px`;
        canvas.style.height = `${viewport.height}px`;

        ctx.scale(dpr, dpr);
        const task = page.render({ canvasContext: ctx, viewport, canvas });
        await task.promise;
        return task;
      } catch {
        return null;
      }
    },
    [doc, scale, rotationMap]
  );

  useEffect(() => {
    if (!doc) return;
    cancelRenderTasks();
    (async () => {
      leftTaskRef.current = await renderPageToCanvas(leftPage, leftCanvasRef.current);
      if (rightPage) {
        rightTaskRef.current = await renderPageToCanvas(rightPage, rightCanvasRef.current);
      }
    })();
    return cancelRenderTasks;
  }, [doc, leftPage, rightPage, scale, rotationMap, renderPageToCanvas, cancelRenderTasks]);

  /* ----------------------------- Filtered Rendering ----------------------------- */
  const isPageVisible = useCallback(
    (pageNum: number) => {
      const mark = getMarkForPage(pageNum);
      if (filter === "both") return true;
      if (filter === "job") return mark.job;
      if (filter === "spare") return mark.spare;
      return true;
    },
    [filter, getMarkForPage]
  );

  // ----- IMPORTANT FIX -----
  // When filter or the underlying marks change, ensure the viewer shows a spread that contains visible pages.
  // If current leftPage is filtered out, move leftPage to the first visible page (keeping odd-left logic).
  useEffect(() => {
    // If there are no pages loaded yet, nothing to do
    if (!numPages) return;

    // If current leftPage is visible under current filter, keep it (don't jump unnecessarily)
    if (isPageVisible(leftPage) || isPageVisible(leftPage + 1)) {
      return;
    }

    // Find first page that matches the filter
    let found: number | null = null;
    for (let i = 1; i <= numPages; i++) {
      if (isPageVisible(i)) {
        found = i;
        break;
      }
    }

    if (found === null) {
      // nothing matches filter, keep leftPage but optionally you could setLeftPage(1)
      // We'll keep leftPage as-is (it will render "No next page" or nothing)
      return;
    }

    // Ensure left page is odd (start of spread)
    const adjusted = found % 2 === 0 ? Math.max(1, found - 1) : found;
    setLeftPage(adjusted);
  }, [filter, value, numPages, leftPage, isPageVisible]); // run when filter or marks change

  /* ----------------------------- Loading Skeleton ----------------------------- */
  const PdfLoadingSkeleton = () => (
    <div className="flex gap-8 p-6">
      {/* Left Page Skeleton */}
      <div className="relative flex-1">
        {/* Toolbar Skeleton */}
        <div className="mb-3 flex items-center justify-between gap-2">
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-8" />
            <Skeleton className="h-8 w-8" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-4 w-8" />
            <Skeleton className="h-4 w-12" />
          </div>
          <Skeleton className="h-8 w-8" />
        </div>
        {/* Page Skeleton */}
        <div className="bg-background ring-border relative flex items-center justify-center overflow-hidden rounded-lg p-3 ring-1">
          <Skeleton className="h-[600px] w-[450px]" />
        </div>
      </div>

      {/* Right Page Skeleton */}
      <div className="relative flex-1">
        {/* Toolbar Skeleton */}
        <div className="mb-3 flex items-center justify-between gap-2">
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-8" />
            <Skeleton className="h-8 w-8" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-4 w-8" />
            <Skeleton className="h-4 w-12" />
          </div>
          <Skeleton className="h-8 w-8" />
        </div>
        {/* Page Skeleton */}
        <div className="bg-background ring-border relative flex items-center justify-center overflow-hidden rounded-lg p-3 ring-1">
          <Skeleton className="h-[600px] w-[450px]" />
        </div>
      </div>
    </div>
  );

  /* ----------------------------- Render ----------------------------- */
  return (
    <div
      className={cn(
        "relative flex h-full w-full select-none flex-col overflow-hidden rounded-xl shadow-xl",
        className
      )}
    >
      {/* Filter Toolbar */}
      <div className="flex items-center justify-between border-b px-6 pb-2 pt-4">
        <div className="font-semibold">Filter Pages</div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              {filter === "both" ? "All Pages" : filter === "job" ? "Job" : "Spare"}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuCheckboxItem
              checked={filter === "job"}
              onCheckedChange={(checked) => {
                const isChecked = Boolean(checked);
                setFilter(isChecked ? "job" : "both");
              }}
            >
              Job
            </DropdownMenuCheckboxItem>
            <DropdownMenuCheckboxItem
              checked={filter === "spare"}
              onCheckedChange={(checked) => {
                const isChecked = Boolean(checked);
                setFilter(isChecked ? "spare" : "both");
              }}
            >
              Spare
            </DropdownMenuCheckboxItem>
            <DropdownMenuCheckboxItem
              checked={filter === "both"}
              onCheckedChange={() => {
                setFilter("both");
              }}
            >
              Both
            </DropdownMenuCheckboxItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Main Content Area with Scroll */}
      <div className="flex-1 overflow-auto">
        {isLoading ? (
          <PdfLoadingSkeleton />
        ) : (
          <div className="flex h-full gap-8 p-6">
            {/* Left Page */}
            {isPageVisible(leftPage) && (
              <div className="relative flex flex-1 flex-col">
                <Toolbar
                  pageNum={leftPage}
                  scale={scale}
                  onZoomIn={zoomIn}
                  onZoomOut={zoomOut}
                  onRotate={() => rotatePage(leftPage)}
                  checked={getMarkForPage(leftPage)}
                  onChangeChecked={(key, val) => setCheck(leftPage, key, val)}
                />
                <div className="flex-1 overflow-auto">
                  <PageCanvas
                    pageNum={leftPage}
                    canvasRef={leftCanvasRef}
                    overlayRef={leftOverlayRef}
                    scale={scale}
                  />
                </div>
              </div>
            )}

            {/* Right Page */}
            {rightPage && isPageVisible(rightPage) ? (
              <div className="relative flex flex-1 flex-col">
                <Toolbar
                  pageNum={rightPage ?? undefined}
                  scale={scale}
                  onZoomIn={zoomIn}
                  onZoomOut={zoomOut}
                  onRotate={() => rightPage && rotatePage(rightPage)}
                  disabled={!rightPage}
                  checked={
                    rightPage ? getMarkForPage(rightPage) : { page: 0, job: false, spare: false }
                  }
                  onChangeChecked={(key, val) => rightPage && setCheck(rightPage, key, val)}
                />
                <div className="flex-1 overflow-auto">
                  <PageCanvas
                    pageNum={rightPage}
                    canvasRef={rightCanvasRef}
                    overlayRef={rightOverlayRef}
                    scale={scale}
                  />
                </div>
              </div>
            ) : (
              <div className="text-muted-foreground flex flex-1 items-center justify-center text-center">
                No next page
              </div>
            )}
          </div>
        )}
      </div>

      {/* Navigation */}
      {!isLoading && (
        <>
          <button
            aria-label="Previous pages"
            onClick={prevSpread}
            className="bg-secondary text-foreground hover:bg-muted absolute left-2 top-1/2 z-10 -translate-y-1/2 rounded-full p-2 shadow"
          >
            <ChevronLeft className="h-6 w-6" />
          </button>
          <button
            aria-label="Next pages"
            onClick={nextSpread}
            className="bg-secondary text-foreground hover:bg-muted absolute right-2 top-1/2 z-10 -translate-y-1/2 rounded-full p-2 shadow"
          >
            <ChevronRight className="h-6 w-6" />
          </button>
        </>
      )}

      {loadingError && (
        <div className="border-border text-destructive-foreground border-t p-3 text-sm">
          {loadingError}
        </div>
      )}
    </div>
  );
}
