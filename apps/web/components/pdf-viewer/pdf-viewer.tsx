"use client";

import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from "react";
import { ChevronLeft, ChevronRight, Trash2, Filter } from "lucide-react";
import { cn } from "@repo/ui/lib/utils";
import { getDocument, type PDFDocumentProxy, type RenderTask } from "pdfjs-dist";
import { usePdfWorker } from "./usePdfWorker";
import { clamp } from "./utils";
import { Button } from "@repo/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { PageCanvas } from "./page-canvas";
import { Toolbar } from "./tool-bar";

/* ----------------------------- Types ----------------------------- */

export interface PageMark {
  page: number;
  job: boolean;
  spare: boolean;
  rotation?: 0 | 90 | 180 | 270;
}

export interface BBox {
  x: number;
  y: number;
  width: number;
  height: number;
  rotation?: 0 | 90 | 180 | 270;
}

export interface PdfViewerProps {
  pdfUrl: string;
  initialPage?: number;
  className?: string;
  value: PageMark[];
  onChange: (marks: PageMark[]) => void;
  bboxes: Record<number, BBox[]>;
  onBBoxesChange: (bboxes: Record<number, BBox[]>) => void;
  enableBBoxes?: boolean; // ✅ new config prop
}

/* ----------------------------- Component ----------------------------- */

export function PdfViewer({
  pdfUrl,
  initialPage = 1,
  className,
  value,
  onChange,
  bboxes,
  onBBoxesChange,
  enableBBoxes = false,
}: PdfViewerProps) {
  usePdfWorker();

  console.log("pdf value", value);

  const [doc, setDoc] = useState<PDFDocumentProxy | null>(null);
  const [numPages, setNumPages] = useState(0);
  const [leftPage, setLeftPage] = useState(() =>
    Math.max(1, initialPage - (initialPage % 2 === 0 ? 1 : 0))
  );
  const [scale, setScale] = useState(1);
  const [rotationMap, setRotationMap] = useState<Record<number, 0 | 90 | 180 | 270>>({});
  const [loadingError, setLoadingError] = useState<string | null>(null);
  const [filter, setFilter] = useState<"job" | "spare" | "both">("both"); // ✅ new filter

  const [drawing, setDrawing] = useState<{
    startX: number;
    startY: number;
    x: number;
    y: number;
    width: number;
    height: number;
    page: number | null;
  } | null>(null);

  const leftCanvasRef = useRef<HTMLCanvasElement | null>(null);
  const rightCanvasRef = useRef<HTMLCanvasElement | null>(null);
  const leftOverlayRef = useRef<HTMLDivElement | null>(null);
  const rightOverlayRef = useRef<HTMLDivElement | null>(null);
  const leftTaskRef = useRef<RenderTask | null>(null);
  const rightTaskRef = useRef<RenderTask | null>(null);

  /* ----------------------------- Navigation ----------------------------- */
  const nextSpread = () => setLeftPage((p) => (p + 2 <= numPages ? p + 2 : p));
  const prevSpread = () => setLeftPage((p) => (p - 2 >= 1 ? p - 2 : p));

  const zoomIn = () => setScale((s) => clamp(Number((s + 0.1).toFixed(2)), 0.5, 3));
  const zoomOut = () => setScale((s) => clamp(Number((s - 0.1).toFixed(2)), 0.5, 3));

  /* ----------------------------- Prevent Browser Zoom ----------------------------- */
  useEffect(() => {
    const handleWheel = (e: WheelEvent) => {
      if (e.ctrlKey) {
        e.preventDefault();
        if (e.deltaY < 0) zoomIn();
        else zoomOut();
      }
    };

    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && (e.key === "+" || e.key === "=")) {
        e.preventDefault();
        zoomIn();
      } else if ((e.ctrlKey || e.metaKey) && e.key === "-") {
        e.preventDefault();
        zoomOut();
      }
    };

    window.addEventListener("wheel", handleWheel, { passive: false });
    window.addEventListener("keydown", handleKeyDown);

    return () => {
      window.removeEventListener("wheel", handleWheel);
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, []);

  /* ----------------------------- Rotation ----------------------------- */
  const rotatePage = (pageNum: number) => {
    setRotationMap((prev) => {
      const current = prev[pageNum] ?? 0;
      const newRotation = ((current + 90) % 360) as 0 | 90 | 180 | 270;

      const updatedMarks = value.map((m) =>
        m.page === pageNum ? { ...m, rotation: newRotation } : m
      );
      const exists = value.find((m) => m.page === pageNum);
      if (!exists)
        updatedMarks.push({
          page: pageNum,
          job: false,
          spare: false,
          rotation: newRotation,
        });
      onChange(updatedMarks);

      const updatedBBoxes = {
        ...bboxes,
        [pageNum]: (bboxes[pageNum] || []).map((b) => ({
          ...b,
          rotation: newRotation,
        })),
      };
      onBBoxesChange(updatedBBoxes);

      return { ...prev, [pageNum]: newRotation };
    });
  };

  /* ----------------------------- Page Marks ----------------------------- */
  const getMarkForPage = (page: number) =>
    value.find((v) => v.page === page) || { page, job: false, spare: false, rotation: 0 };

  const setCheck = (pageNum: number, key: "job" | "spare", val: boolean) => {
    const updated = value.map((m) => (m.page === pageNum ? { ...m, [key]: val } : m));
    const exists = value.find((m) => m.page === pageNum);
    if (!exists)
      updated.push({
        page: pageNum,
        job: key === "job" ? val : false,
        spare: key === "spare" ? val : false,
        rotation: rotationMap[pageNum] ?? 0,
      });
    onChange(updated);
  };

  /* ----------------------------- PDF Load ----------------------------- */
  useEffect(() => {
    let cancelled = false;
    setLoadingError(null);

    getDocument({ url: pdfUrl })
      .promise.then((pdf) => {
        if (cancelled) return;
        setDoc(pdf);
        setNumPages(pdf.numPages);
        setLeftPage((prev) => (prev % 2 === 0 ? prev - 1 : prev));
      })
      .catch(() => setLoadingError("Failed to load PDF."));

    return () => {
      cancelled = true;
    };
  }, [pdfUrl]);

  const rightPage = useMemo(
    () => (leftPage + 1 <= numPages ? leftPage + 1 : null),
    [leftPage, numPages]
  );

  /* ----------------------------- Rendering ----------------------------- */
  const cancelRenderTasks = useCallback(() => {
    leftTaskRef.current?.cancel();
    rightTaskRef.current?.cancel();
    leftTaskRef.current = null;
    rightTaskRef.current = null;
  }, []);

  const renderPageToCanvas = useCallback(
    async (pageNum: number, canvas: HTMLCanvasElement | null): Promise<RenderTask | null> => {
      if (!doc || !canvas) return null;
      try {
        const page = await doc.getPage(pageNum);
        const dpr = window.devicePixelRatio || 1;
        const rotation = rotationMap[pageNum] ?? 0;
        const viewport = page.getViewport({ scale, rotation });
        const ctx = canvas.getContext("2d", { alpha: false });
        if (!ctx) return null;

        canvas.width = viewport.width * dpr;
        canvas.height = viewport.height * dpr;
        canvas.style.width = `${viewport.width}px`;
        canvas.style.height = `${viewport.height}px`;

        ctx.scale(dpr, dpr);
        const task = page.render({ canvasContext: ctx, viewport });
        await task.promise;
        return task;
      } catch {
        return null;
      }
    },
    [doc, scale, rotationMap]
  );

  useEffect(() => {
    if (!doc) return;
    cancelRenderTasks();
    (async () => {
      leftTaskRef.current = await renderPageToCanvas(leftPage, leftCanvasRef.current);
      if (rightPage) {
        rightTaskRef.current = await renderPageToCanvas(rightPage, rightCanvasRef.current);
      }
    })();
    return cancelRenderTasks;
  }, [doc, leftPage, rightPage, scale, rotationMap, renderPageToCanvas]);

  /* ----------------------------- Drawing ----------------------------- */
  const handleMouseDown = (e: React.MouseEvent, page: number) => {
    if (!enableBBoxes) return; // ✅ disable bbox interaction
    const overlay = e.currentTarget as HTMLDivElement;
    const rect = overlay.getBoundingClientRect();
    const startX = e.clientX - rect.left;
    const startY = e.clientY - rect.top;
    setDrawing({ startX, startY, x: startX, y: startY, width: 0, height: 0, page });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!enableBBoxes || !drawing) return;
    const overlay = drawing.page === leftPage ? leftOverlayRef.current : rightOverlayRef.current;
    if (!overlay) return;
    const rect = overlay.getBoundingClientRect();
    const currentX = e.clientX - rect.left;
    const currentY = e.clientY - rect.top;
    setDrawing({
      ...drawing,
      x: Math.min(currentX, drawing.startX),
      y: Math.min(currentY, drawing.startY),
      width: Math.abs(currentX - drawing.startX),
      height: Math.abs(currentY - drawing.startY),
    });
  };

  const handleMouseUp = () => {
    if (!enableBBoxes || !drawing || drawing.width < 5 || drawing.height < 5) {
      setDrawing(null);
      return;
    }
    const page = drawing.page!;
    const rotation = rotationMap[page] ?? 0;
    onBBoxesChange({
      ...bboxes,
      [page]: [...(bboxes[page] || []), { ...drawing, rotation }],
    });
    setDrawing(null);
  };

  /* ----------------------------- Filtered Rendering ----------------------------- */
  const isPageVisible = (pageNum: number) => {
    const mark = getMarkForPage(pageNum);
    if (filter === "both") return true;
    if (filter === "job") return mark.job;
    if (filter === "spare") return mark.spare;
    return true;
  };

  // ----- IMPORTANT FIX -----
  // When filter or the underlying marks change, ensure the viewer shows a spread that contains visible pages.
  // If current leftPage is filtered out, move leftPage to the first visible page (keeping odd-left logic).
  useEffect(() => {
    // If there are no pages loaded yet, nothing to do
    if (!numPages) return;

    // If current leftPage is visible under current filter, keep it (don't jump unnecessarily)
    if (isPageVisible(leftPage) || isPageVisible(leftPage + 1)) {
      return;
    }

    // Find first page that matches the filter
    let found: number | null = null;
    for (let i = 1; i <= numPages; i++) {
      if (isPageVisible(i)) {
        found = i;
        break;
      }
    }

    if (found === null) {
      // nothing matches filter, keep leftPage but optionally you could setLeftPage(1)
      // We'll keep leftPage as-is (it will render "No next page" or nothing)
      return;
    }

    // Ensure left page is odd (start of spread)
    const adjusted = found % 2 === 0 ? Math.max(1, found - 1) : found;
    setLeftPage(adjusted);
  }, [filter, value, numPages]); // run when filter or marks change

  /* ----------------------------- Render ----------------------------- */
  return (
    <div
      className={cn(
        "relative w-full max-w-[1400px] select-none overflow-hidden rounded-xl shadow-xl",
        className
      )}
    >
      {/* ✅ Filter Toolbar */}
      <div className="flex items-center justify-between px-6 pt-4">
        <div className="font-semibold">Filter Pages</div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              {filter === "both" ? "All Pages" : filter === "job" ? "Job" : "Spare"}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {/* Use checkbox items but make toggling logic explicit:
                - checking a type sets that filter
                - unchecking it returns to "both" */}
            <DropdownMenuCheckboxItem
              checked={filter === "job"}
              onCheckedChange={(checked) => {
                // checked may be boolean | "indeterminate" depending on lib; coerce to boolean
                const isChecked = Boolean(checked);
                setFilter(isChecked ? "job" : "both");
              }}
            >
              Job
            </DropdownMenuCheckboxItem>
            <DropdownMenuCheckboxItem
              checked={filter === "spare"}
              onCheckedChange={(checked) => {
                const isChecked = Boolean(checked);
                setFilter(isChecked ? "spare" : "both");
              }}
            >
              Spare
            </DropdownMenuCheckboxItem>
            <DropdownMenuCheckboxItem
              checked={filter === "both"}
              onCheckedChange={() => {
                setFilter("both");
              }}
            >
              Both
            </DropdownMenuCheckboxItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="flex gap-8 p-6">
        {/* Left Page */}
        {isPageVisible(leftPage) && (
          <div className="relative flex-1 overflow-auto">
            <Toolbar
              pageNum={leftPage}
              scale={scale}
              onZoomIn={zoomIn}
              onZoomOut={zoomOut}
              onRotate={() => rotatePage(leftPage)}
              checked={getMarkForPage(leftPage)}
              onChangeChecked={(key, val) => setCheck(leftPage, key, val)}
              onClearBBoxes={() =>
                onBBoxesChange({
                  ...bboxes,
                  [leftPage]: [],
                })
              }
            />
            <PageCanvas
              pageNum={leftPage}
              canvasRef={leftCanvasRef}
              overlayRef={leftOverlayRef}
              bboxes={bboxes[leftPage] || []}
              drawing={drawing}
              handleMouseDown={handleMouseDown}
              handleMouseMove={handleMouseMove}
              handleMouseUp={handleMouseUp}
            />
          </div>
        )}

        {/* Right Page */}
        {rightPage && isPageVisible(rightPage) ? (
          <div className="relative flex-1 overflow-auto">
            <Toolbar
              pageNum={rightPage ?? undefined}
              scale={scale}
              onZoomIn={zoomIn}
              onZoomOut={zoomOut}
              onRotate={() => rightPage && rotatePage(rightPage)}
              disabled={!rightPage}
              checked={
                rightPage ? getMarkForPage(rightPage) : { page: 0, job: false, spare: false }
              }
              onChangeChecked={(key, val) => rightPage && setCheck(rightPage, key, val)}
              onClearBBoxes={() => {
                if (!rightPage) return;
                onBBoxesChange({
                  ...bboxes,
                  [rightPage]: [],
                });
              }}
            />
            <PageCanvas
              pageNum={rightPage}
              canvasRef={rightCanvasRef}
              overlayRef={rightOverlayRef}
              bboxes={bboxes[rightPage] || []}
              drawing={drawing}
              handleMouseDown={handleMouseDown}
              handleMouseMove={handleMouseMove}
              handleMouseUp={handleMouseUp}
            />
          </div>
        ) : (
          <div className="text-muted-foreground flex h-40 items-center justify-center text-center">
            No next page
          </div>
        )}
      </div>

      {/* Navigation */}
      <button
        aria-label="Previous pages"
        onClick={prevSpread}
        className="bg-secondary text-foreground hover:bg-muted absolute left-2 top-1/2 -translate-y-1/2 rounded-full p-2 shadow"
      >
        <ChevronLeft className="h-6 w-6" />
      </button>
      <button
        aria-label="Next pages"
        onClick={nextSpread}
        className="bg-secondary text-foreground hover:bg-muted absolute right-2 top-1/2 -translate-y-1/2 rounded-full p-2 shadow"
      >
        <ChevronRight className="h-6 w-6" />
      </button>

      {/* Global Clear All */}
      {enableBBoxes && (
        <div className="flex justify-center pb-4">
          <Button variant="destructive" onClick={() => onBBoxesChange({})}>
            <Trash2 className="mr-2 h-4 w-4" /> Clear All BBoxes
          </Button>
        </div>
      )}

      {loadingError && (
        <div className="border-border text-destructive-foreground border-t p-3 text-sm">
          {loadingError}
        </div>
      )}
    </div>
  );
}
