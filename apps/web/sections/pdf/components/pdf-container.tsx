"use client";

import React, { useState, useEffect } from "react";
import { PdfViewer } from "@/components/pdf-viewer";
import { BBox, PageMark } from "@/components/pdf-viewer/pdf-viewer";
import { useProjectFile } from "../hooks/api/file.get.slice";
import { Skeleton } from "@repo/ui/components/skeleton";
import { Icons } from "@repo/ui/components/icons";
import { instance } from "@/axios-instance";

interface PdfContainerProps {
  projectId: string;
  fileId: string;
}

export const PdfContainer = ({ projectId, fileId }: PdfContainerProps) => {
  const [marks, setMarks] = useState<PageMark[]>([]);
  const [bboxes, setBboxes] = useState<Record<number, BBox[]>>({});
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [pdfError, setPdfError] = useState<string | null>(null);

  const { data: fileData, isLoading, error } = useProjectFile(projectId, fileId);

  // Create authenticated PDF URL
  useEffect(() => {
    if (!fileData) return;

    const createAuthenticatedUrl = async () => {
      try {
        // Get the file as a blob with authentication headers
        const response = await instance.get(`/projects/${projectId}/files/${fileId}`, {
          responseType: "blob",
        });

        // Create a blob URL for the PDF viewer
        const blob = new Blob([response.data], { type: "application/pdf" });
        const url = URL.createObjectURL(blob);
        setPdfUrl(url);
      } catch (err) {
        console.error("Failed to load PDF:", err);
        setPdfError("Failed to load PDF file");
      }
    };

    createAuthenticatedUrl();

    // Cleanup blob URL on unmount
    return () => {
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
      }
    };
  }, [fileData, projectId, fileId]);

  if (isLoading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="space-y-4">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-96 w-full" />
        </div>
      </div>
    );
  }

  if (error || pdfError) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="flex max-w-md items-center gap-3 rounded-lg border border-red-200 bg-red-50 p-4 text-red-800">
          <Icons.alertCircle className="h-5 w-5 flex-shrink-0" />
          <p className="text-sm">
            {pdfError || "Failed to load PDF file. Please try again later."}
          </p>
        </div>
      </div>
    );
  }

  if (!fileData) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="flex max-w-md items-center gap-3 rounded-lg border border-yellow-200 bg-yellow-50 p-4 text-yellow-800">
          <Icons.alertCircle className="h-5 w-5 flex-shrink-0" />
          <p className="text-sm">File not found or you don't have permission to view it.</p>
        </div>
      </div>
    );
  }

  if (!pdfUrl) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="space-y-4">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-96 w-full" />
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen w-full">
      <PdfViewer
        pdfUrl={pdfUrl}
        initialPage={1}
        value={marks}
        onChange={setMarks}
        bboxes={bboxes}
        onBBoxesChange={setBboxes}
        className="h-full w-full"
      />
    </div>
  );
};
