from pydantic import BaseModel, Field
from typing import List, Optional, Literal


class ImageDiagramDetail(BaseModel):
    """Details about images or diagrams found in the document."""
    name: str = Field(description="Name or description of the diagram/image")
    type: Literal["schematic", "photo", "drawing", "table", "chart", "plan"] = Field(
        description="Type of visual element"
    )


class MistralOCRPageResponse(BaseModel):
    """Structured response from Mistral OCR for a single page."""
    
    extracted_text: str = Field(
        description="Full text extracted from the image"
    )
    
    languages_detected: List[str] = Field(
        default=["en"],
        description="List of ISO 639-1 language codes detected"
    )
    
    quality_score: int = Field(
        ge=1, le=10,
        description="Quality score of the image/text extraction (1-10)"
    )
    
    pms_likelihood_score: int = Field(
        ge=0, le=10,
        description="PMS likelihood score (0-10) based on visual analysis"
    )
    
    pms_reason: str = Field(
        description="Short explanation for the PMS classification"
    )
    
    pms_classification: Literal["PMS", "Non-PMS"] = Field(
        description="Classification as PMS or Non-PMS document"
    )
    
    main_machinery: Optional[str] = Field(
        default=None,
        description="Main machinery or equipment name, or null if none found"
    )
    
    images_or_diagrams_present: bool = Field(
        description="Whether images or diagrams are present on the page"
    )
    
    image_diagram_details: List[ImageDiagramDetail] = Field(
        default_factory=list,
        description="Details about images/diagrams found"
    )
    
    total_image_count: int = Field(
        ge=0,
        description="Total number of images/diagrams on the page"
    )
    
    summary: str = Field(
        description="1-5 lines summarizing the page content, processing only the title, heading etc, avoid data with table but add a content summary"
    )


class MistralOCRDocumentResponse(BaseModel):
    """Aggregated response for the entire document."""
    
    extracted_text: str = Field(
        description="Combined text from all pages"
    )
    
    overall_classification: Literal["PMS", "Non-PMS"] = Field(
        description="Overall document classification"
    )
    
    avg_pms_likelihood_score: float = Field(
        ge=0, le=10,
        description="Average PMS likelihood score across all pages"
    )
    
    pms_pages_count: int = Field(
        ge=0,
        description="Number of pages classified as PMS"
    )
    
    total_pages: int = Field(
        ge=1,
        description="Total number of pages processed"
    )
    
    main_machinery: Optional[str] = Field(
        default=None,
        description="Main machinery identified in the document"
    )
    
    page_details: List[dict] = Field(
        description="Detailed analysis for each page"
    )
