from pydantic import BaseModel, Field
from typing import Optional, Literal

class PageResult(BaseModel):
    has_spares: bool = Field(..., description="True if page contains a spare parts list")
    has_schedule: bool = Field(..., description="True if page contains a maintenance schedule")
    confidence: float = Field(..., description="Confidence score between 0.0 and 1.0")
    reason: str = Field(..., description="Short one-line reasoning")
    page_number: Optional[int] = Field(None, description="Page number of the result")