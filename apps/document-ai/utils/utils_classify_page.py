import base64
from pathlib import Path
import fitz  # PyMuPDF

def pdf_page_to_image(pdf_path: str, page_number: int, zoom: float = 2.0):
    """Convert a single PDF page to base64 PNG (data URI)."""
    pdf_path = Path(pdf_path)
    with fitz.open(pdf_path) as doc:
        page = doc.load_page(page_number - 1)
        pix = page.get_pixmap(matrix=fitz.Matrix(zoom, zoom))
        img_bytes = pix.tobytes(output="png")
        img_b64 = base64.b64encode(img_bytes).decode("utf-8")
        data_uri = f"data:image/png;base64,{img_b64}"
    return {
        "page_number": page_number,
        "filename": f"{pdf_path.stem}_p{page_number}.png",
        "image_base64": data_uri,
    }

def pdf_to_base64_images(pdf_path: str, zoom: float = 2.0):
    """Convert all pages to base64 images."""
    pdf_path = Path(pdf_path)
    with fitz.open(pdf_path) as doc:
        images = []
        for i in range(len(doc)):
            page = doc.load_page(i)
            pix = page.get_pixmap(matrix=fitz.Matrix(zoom, zoom))
            img_bytes = pix.tobytes(output="png")
            img_b64 = base64.b64encode(img_bytes).decode("utf-8")
            data_uri = f"data:image/png;base64,{img_b64}"
            images.append({
                "page_number": i + 1,
                "filename": f"{pdf_path.stem}_p{i+1}.png",
                "image_base64": data_uri,
            })
    return images
