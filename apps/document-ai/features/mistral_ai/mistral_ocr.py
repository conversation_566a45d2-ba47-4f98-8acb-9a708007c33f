import base64
import os
from typing import List
from io import BytesIO
from pdf2image import convert_from_bytes
from PIL import Image
from mistralai import Mistral

def pdf_to_images(pdf_bytes: bytes, max_pages: int = 7) -> List[Image.Image]:
    """Convert first few pages of PDF to PIL Images using pdf2image."""
    try:
        pil_images = convert_from_bytes(
            pdf_bytes,
            dpi=300,  # High DPI for better OCR
            first_page=1,
            last_page=max_pages,
            fmt='PNG'
        )
        return pil_images
    except Exception as e:
        print(f"Error rendering PDF pages: {e}")
        return []

def pil_image_to_base64(pil_image: Image.Image) -> str:
    """Convert PIL Image to base64 string."""
    buffered = BytesIO()
    pil_image.save(buffered, format="PNG")
    return base64.b64encode(buffered.getvalue()).decode('utf-8')

def pdf_to_images_base64(pdf_bytes: bytes, max_pages: int = 7) -> List[str]:
    """Convert PDF pages to base64-encoded images."""
    pil_images = pdf_to_images(pdf_bytes, max_pages)
    return [pil_image_to_base64(img) for img in pil_images]

async def extract_text_with_mistral(pdf_bytes: bytes) -> str:
    """
    Extract text from PDF using Mistral's vision model for OCR.
    """
    try:
        # Convert PDF pages to images
        images = pdf_to_images_base64(pdf_bytes, max_pages=7)  # Process first 7 pages

        if not images:
            return ""

        # Initialize Mistral client
        api_key = os.getenv("MISTRAL_API_KEY")
        if not api_key:
            raise ValueError("MISTRAL_API_KEY not found in environment variables")

        client = Mistral(api_key=api_key)

        extracted_texts = []

        # Process each page
        for i, image_b64 in enumerate(images):
            print(f"Processing page {i+1}/{len(images)} with Mistral OCR...")

            # Prepare the message for Mistral
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": """
                            Extract all text from this image. Focus on:
                            - Document titles and headings
                            - Part numbers and model numbers
                            - Technical specifications
                            - Maintenance procedures
                            - Any machinery or equipment names
                            Return only the extracted text, maintaining the original structure and formatting as much as possible.
                            """
                        },
                        {
                            "type": "image_url",
                            "image_url": f"data:image/png;base64,{image_b64}"
                        }
                    ]
                }
            ]

            # Make API call to Mistral
            response = client.chat.complete(
                model="pixtral-12b-2409",  # Mistral's vision model
                messages=messages,
                max_tokens=2000,
                temperature=0.1
            )

            page_text = response.choices[0].message.content
            extracted_texts.append(f"=== Page {i+1} ===\n{page_text}\n")

        # Combine all extracted text
        full_text = "\n".join(extracted_texts)
        return full_text

    except Exception as e:
        print(f"Error in Mistral OCR: {e}")
        return ""