from fastapi import FastAPI, UploadFile, Form
from features.classify_doc import handler
# from functions.extract_text_with_mistral import handler as ocr_handler
import base64
from dotenv import load_dotenv
import os

load_dotenv()  # loads .env


app = FastAPI(
    title="Marine Document Classification API",
    description="API for classifying marine documents using Mistral OCR and ChatGPT",
    version="1.0.0"
)

@app.post("/classify")
async def classify_pdf(file: UploadFile):
    """Classify a marine PDF document as PMS or Non-PMS with machinery identification."""
    pdf_bytes = await file.read()
    file_base64 = base64.b64encode(pdf_bytes).decode("utf-8")

    # emulate a serverless event
    event = {"file_base64": file_base64, "filename": file.filename}

    result = await handler(event)
    return result

@app.post("/ocr")
async def extract_text_from_pdf(file: UploadFile):
    """Extract text from PDF using Mistral OCR."""
    pdf_bytes = await file.read()
    file_base64 = base64.b64encode(pdf_bytes).decode("utf-8")

    # emulate a serverless event
    event = {"file_base64": file_base64, "filename": file.filename}

    result = await ocr_handler(event)
    return result

@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Marine Document Classification API",
        "endpoints": {
            "/classify": "POST - Classify marine documents",
            "/ocr": "POST - Extract text using Mistral OCR",
            "/docs": "GET - Interactive API documentation"
        }
    }
