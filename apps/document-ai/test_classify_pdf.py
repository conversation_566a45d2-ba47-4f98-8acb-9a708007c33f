#!/usr/bin/env python3
"""
Test script for marine document classification - processes all PDFs in the pdf folder
and creates JSON dumps for each file.
"""

import asyncio
import base64
import json
import os
import glob
from datetime import datetime
from features.open_ai.classify_pdf import handler

async def classify_single_pdf(pdf_path):
    """Classify a single PDF file and return the result."""

    filename = os.path.basename(pdf_path)

    try:
        # Read the PDF file
        with open(pdf_path, "rb") as f:
            pdf_bytes = f.read()

        # Encode to base64
        file_base64 = base64.b64encode(pdf_bytes).decode("utf-8")

        # Create event object
        event = {
            "file_base64": file_base64,
            "filename": filename
        }

        print(f"🔍 Classifying {filename}...")
        print(f"📄 File size: {len(pdf_bytes):,} bytes")

        # Call the handler
        result = await handler(event)

        # Add metadata
        result["metadata"] = {
            "filename": filename,
            "file_path": pdf_path,
            "file_size_bytes": len(pdf_bytes),
            "processed_at": datetime.now().isoformat(),
            "processing_status": "success" if "error" not in result else "error"
        }

        return result

    except Exception as e:
        error_result = {
            "error": str(e),
            "metadata": {
                "filename": filename,
                "file_path": pdf_path,
                "processed_at": datetime.now().isoformat(),
                "processing_status": "error"
            }
        }
        return error_result

async def test_all_pdfs():
    """Process all PDF files in the pdf folder and create JSON dumps."""

    # Define paths
    pdf_folder = "pdf"
    output_folder = "classification_results"

    # Create output folder if it doesn't exist
    os.makedirs(output_folder, exist_ok=True)

    # Find all PDF files
    pdf_pattern = os.path.join(pdf_folder, "*.pdf")
    pdf_files = glob.glob(pdf_pattern)

    if not pdf_files:
        print(f"❌ No PDF files found in {pdf_folder} folder")
        return

    print(f"📁 Found {len(pdf_files)} PDF files in {pdf_folder} folder")
    print("=" * 60)

    all_results = []

    for i, pdf_path in enumerate(pdf_files, 1):
        filename = os.path.basename(pdf_path)
        print(f"\n[{i}/{len(pdf_files)}] Processing: {filename}")

        # Classify the PDF
        result = await classify_single_pdf(pdf_path)
        all_results.append(result)

        # Create individual JSON file
        safe_filename = filename.replace(".pdf", "").replace(" ", "_").replace("-", "_")
        json_filename = f"{safe_filename}_classification.json"
        json_path = os.path.join(output_folder, json_filename)

        with open(json_path, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)

        # Print results
        print(f"💾 Saved: {json_path}")

        if "error" in result:
            print(f"❌ Error: {result['error']}")
        else:
            classification = result.get("classification", "Unknown")
            main_machinery = result.get("main_machinery", "N/A")

            print(f"✅ Classification: {classification}")
            if classification == "PMS" and main_machinery:
                print(f"🔧 Main Machinery: {main_machinery}")

        print("-" * 40)

    # Create summary JSON with all results
    summary_path = os.path.join(output_folder, "classification_summary.json")
    summary = {
        "processed_at": datetime.now().isoformat(),
        "total_files": len(pdf_files),
        "successful_classifications": len([r for r in all_results if "error" not in r]),
        "failed_classifications": len([r for r in all_results if "error" in r]),
        "results": all_results
    }

    with open(summary_path, "w", encoding="utf-8") as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)

    print(f"\n📊 Summary:")
    print(f"   Total files processed: {summary['total_files']}")
    print(f"   Successful: {summary['successful_classifications']}")
    print(f"   Failed: {summary['failed_classifications']}")
    print(f"   Summary saved: {summary_path}")

if __name__ == "__main__":
    asyncio.run(test_all_pdfs())
