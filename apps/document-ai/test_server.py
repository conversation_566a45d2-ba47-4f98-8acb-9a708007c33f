#!/usr/bin/env python3
"""
Simple test script to verify the serverless application is working.
"""
import requests
import os

def test_server():
    """Test if the server is running and responding."""
    try:
        # Test the root endpoint (should return 404 but server is running)
        response = requests.get("http://localhost:8000/")
        print(f"Server status: {response.status_code}")
        
        # Test the docs endpoint
        docs_response = requests.get("http://localhost:8000/docs")
        print(f"Docs endpoint status: {docs_response.status_code}")
        
        if docs_response.status_code == 200:
            print("✅ Server is running successfully!")
            print("📖 API documentation available at: http://localhost:8000/docs")
            print("🔧 You can test the /classify endpoint using the interactive docs")
        else:
            print("❌ Server might not be running properly")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure it's running on http://localhost:8000")
        print("💡 Run: cd apps/serverless && python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000")

def check_env():
    """Check if environment variables are properly loaded."""
    from dotenv import load_dotenv
    load_dotenv()
    
    api_key = os.getenv('OPENAI_API_KEY')
    if api_key:
        print(f"✅ OPENAI_API_KEY is loaded (starts with: {api_key[:20]}...)")
    else:
        print("❌ OPENAI_API_KEY not found in environment")

if __name__ == "__main__":
    print("🧪 Testing serverless application...")
    print("\n1. Checking environment variables:")
    check_env()
    
    print("\n2. Testing server connectivity:")
    test_server()
